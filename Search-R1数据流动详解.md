# Search-R1 数据流动详解：从数据处理到训练推理

## 概述

本文档详细说明Search-R1中数据从原始QA对到最终推理输出的完整流动过程，包括向量的具体变化和处理逻辑。

## 1. 数据预处理阶段

### 1.1 原始数据格式
```python
# 原始NQ数据示例
raw_data = {
    "question": "Who is the current president of the United States",
    "golden_answers": ["<PERSON>", "<PERSON><PERSON>"]
}
```

### 1.2 数据预处理 (scripts/data_process/nq_search.py)

**步骤1：构建提示模板**
```python
def make_prefix(dp, template_type):
    question = dp['question']
    if question[-1] != '?':
        question += '?'
    
    prefix = f"""Answer the given question. \
You must conduct reasoning inside <think> and </think> first every time you get new information. \
After reasoning, if you find you lack some knowledge, you can call a search engine by <search> query </search> and it will return the top searched results between <information> and </information>. \
You can search as many times as your want. \
If you find no further external knowledge needed, you can directly provide the answer inside <answer> and </answer>, without detailed illustrations. For example, <answer> Beijing </answer>. Question: {question}\n"""
    return prefix
```

**步骤2：转换为训练格式**
```python
processed_data = {
    "data_source": "nq",
    "prompt": [{
        "role": "user", 
        "content": "Answer the given question. You must conduct reasoning inside <think> and </think>... Question: Who is the current president of the United States?\n"
    }],
    "ability": "fact-reasoning",
    "reward_model": {
        "style": "rule",
        "ground_truth": {"target": ["Joe Biden", "Biden"]}
    },
    "extra_info": {"split": "train", "index": 0}
}
```

## 2. 训练阶段数据流动

### 2.1 初始化阶段

**Tokenizer处理**
```python
# 输入文本
text = "Answer the given question. You must conduct reasoning... Question: Who is the current president of the United States?\n"

# Tokenization
tokenizer = AutoTokenizer.from_pretrained("meta-llama/Llama-3.2-3B")
input_ids = tokenizer.encode(text)
# 示例结果: [1, 22550, 279, 2728, 3488, 13, 1472, 2011, 6929, 33811, ...]
# 形状: [1, seq_len] 其中seq_len约为100-200个token
```

**初始状态构建**
```python
# generation.py中的初始化
original_left_side = {'input_ids': initial_input_ids[:, -self.config.max_start_length:]}
# 形状: [batch_size, max_start_length] 例如 [32, 2048]

original_right_side = {
    'responses': initial_input_ids[:, []], 
    'responses_with_info_mask': initial_input_ids[:, []]
}
# 形状: [batch_size, 0] - 初始为空
```

### 2.2 生成循环中的数据流动

**第一轮生成**

1. **输入准备**
```python
# rollings.batch包含:
{
    'input_ids': tensor([[1, 22550, 279, 2728, ...]]),      # [batch_size, current_length]
    'attention_mask': tensor([[1, 1, 1, 1, ...]]),          # [batch_size, current_length] 
    'position_ids': tensor([[0, 1, 2, 3, ...]])             # [batch_size, current_length]
}
```

2. **模型生成**
```python
# vLLM生成新的响应
gen_output = self.actor_rollout_wg.generate_sequences(rollings)
# gen_output.batch['responses']: [batch_size, response_length]
# 例如生成: "<think>I need to find information about the current US president.</think><search>current president United States 2024</search>"
```

3. **响应后处理**
```python
def _postprocess_responses(self, responses):
    responses_str = self.tokenizer.batch_decode(responses, skip_special_tokens=True)
    # 截断到第一个完整的action
    responses_str = [resp.split('</search>')[0] + '</search>' 
                    if '</search>' in resp 
                    else resp.split('</answer>')[0] + '</answer>'
                    if '</answer>' in resp 
                    else resp
                    for resp in responses_str]
    return self._batch_tokenize(responses_str)
```

4. **动作解析和执行**
```python
def postprocess_predictions(self, predictions):
    actions = []
    contents = []
    for prediction in predictions:
        pattern = r'<(search|answer)>(.*?)</\1>'
        match = re.search(pattern, prediction, re.DOTALL)
        if match:
            action = match.group(1)  # "search" 或 "answer"
            content = match.group(2).strip()  # 查询内容或答案内容
        else:
            action = None
            content = ''
        actions.append(action)
        contents.append(content)
    return actions, contents

# 示例结果:
# actions = ["search"]
# contents = ["current president United States 2024"]
```

### 2.3 搜索执行和结果处理

**搜索API调用**
```python
def batch_search(self, queries):
    payload = {
        "queries": ["current president United States 2024"],
        "topk": 3,
        "return_scores": True
    }
    results = requests.post("http://127.0.0.1:8000/retrieve", json=payload).json()
    
    # 搜索结果格式化
    formatted_results = self._passages2string(results['result'][0])
    # 返回类似: "Doc 1(Title: Joe Biden) Joe Biden is the 46th president...\nDoc 2(Title: President of the United States) The current president...\n"
```

**观察结果处理**
```python
def execute_predictions(self, predictions, pad_token, active_mask, do_search=True):
    # 对于搜索动作
    if action == 'search':
        next_obs.append(f'\n\n<information>{search_results.strip()}</information>\n\n')
        dones.append(0)  # 继续生成
        valid_action.append(1)
        is_search.append(1)
    
    # next_obs示例:
    # "\n\n<information>Doc 1(Title: Joe Biden) Joe Biden is the 46th president of the United States...\nDoc 2(Title: President of the United States) The current president is Joe Biden...</information>\n\n"
```

**状态更新**
```python
def _update_rolling_state(self, rollings, cur_responses, next_obs_ids):
    # 拼接: 原始输入 + 当前响应 + 观察结果
    new_input_ids = self.tensor_fn.concatenate_with_padding([
        rollings.batch['input_ids'],    # [batch_size, prev_length]
        cur_responses,                  # [batch_size, response_length] 
        next_obs_ids                    # [batch_size, obs_length]
    ])
    # 结果形状: [batch_size, prev_length + response_length + obs_length]
    
    # 创建attention mask
    new_attention_mask = self.tensor_fn.create_attention_mask(new_input_ids)
    # 形状: [batch_size, total_length], 1表示有效token，0表示padding
    
    # 截断到最大长度
    effective_len = new_attention_mask.sum(dim=1).max()
    max_len = min(self.config.max_prompt_length, effective_len)
    
    return DataProto.from_dict({
        'input_ids': new_input_ids[:, -max_len:],
        'position_ids': new_position_ids[:, -max_len:],
        'attention_mask': new_attention_mask[:, -max_len:]
    })
```

### 2.4 第二轮生成（基于搜索结果）

**更新后的输入**
```python
# 现在的input_ids包含:
# [原始问题] + [第一轮思考和搜索] + [搜索结果] 
# 具体token序列类似:
# [1, 22550, 279, ...] + [27, think, 29, I, need, ...] + [27, information, 29, Doc, 1, ...]
```

**第二轮生成**
```python
# 模型基于完整上下文生成最终答案
# 生成内容: "<think>Based on the search results, Joe Biden is the current president.</think><answer>Joe Biden</answer>"
```

### 2.5 奖励计算

**奖励计算过程**
```python
def compute_score_em(solution_str, ground_truth, format_score=0., score=1.):
    # 提取答案
    answer_pattern = r'<answer>(.*?)</answer>'
    matches = list(re.finditer(answer_pattern, solution_str, re.DOTALL))
    if len(matches) <= 1:
        return 0  # 没有找到答案标签
    
    answer = matches[-1].group(1).strip()  # "Joe Biden"
    
    # 精确匹配检查
    def normalize_answer(s):
        # 去除标点、冠词，转小写等
        return re.sub(r"\b(a|an|the)\b", " ", s.lower()).strip()
    
    normalized_prediction = normalize_answer(answer)  # "joe biden"
    
    for golden_answer in ground_truth['target']:  # ["Joe Biden", "Biden"]
        if normalize_answer(golden_answer) == normalized_prediction:
            return score  # 返回1.0
    
    return format_score  # 返回0.0
```

**奖励张量构建**
```python
# 在RewardManager中
reward_tensor = torch.zeros_like(data.batch['responses'], dtype=torch.float32)
# 形状: [batch_size, response_length]

# 只在最后一个有效token位置给出奖励
reward_tensor[i, valid_response_length - 1] = score
# 例如: reward_tensor[0, 45] = 1.0 (如果答案正确)
```

## 3. 推理阶段数据流动

### 3.1 推理初始化

```python
# infer.py中的设置
question = "Mike Barnett negotiated many contracts including which player that went on to become general manager of CSKA Moscow?"
prompt = f"""Answer the given question. You must conduct reasoning... Question: {question}\n"""

# Tokenization
input_ids = tokenizer.encode(prompt, return_tensors='pt')
# 形状: [1, prompt_length]
```

### 3.2 推理循环

**循环结构**
```python
while True:
    # 1. 生成响应
    outputs = model.generate(
        input_ids,
        max_new_tokens=1024,
        stopping_criteria=stopping_criteria,  # 遇到</search>停止
        temperature=0.7
    )
    
    # 2. 检查是否结束
    if outputs[0][-1].item() in curr_eos:  # EOS token
        break
    
    # 3. 解析搜索查询
    generated_tokens = outputs[0][input_ids.shape[1]:]
    output_text = tokenizer.decode(generated_tokens, skip_special_tokens=True)
    
    # 4. 执行搜索
    query = get_query(tokenizer.decode(outputs[0], skip_special_tokens=True))
    if query:
        search_results = search(query)
        search_text = f'\n\n{output_text}<information>{search_results}</information>\n\n'
        prompt += search_text
        input_ids = tokenizer.encode(prompt, return_tensors='pt')
```

**具体示例流程**

第一轮：
```
输入: "Answer the given question... Question: Mike Barnett negotiated..."
输出: "<think>I need to search for information about Mike Barnett and his contract negotiations.</think><search>Mike Barnett contract negotiations hockey</search>"
```

搜索结果：
```
"Doc 1(Title: Mike Barnett) Mike Barnett is a sports agent who negotiated contracts for several NHL players including Sergei Fedorov..."
```

第二轮：
```
输入: 原始问题 + 第一轮输出 + 搜索结果
输出: "<think>Based on the search results, I can see that Mike Barnett negotiated contracts for Sergei Fedorov, who later became general manager of CSKA Moscow.</think><answer>Sergei Fedorov</answer>"
```

### 3.3 向量维度变化总结

```python
# 训练阶段向量维度变化
初始输入: [batch_size, initial_length]           # [32, 150]
第一轮生成: [batch_size, response_length]        # [32, 50] 
搜索结果: [batch_size, obs_length]              # [32, 200]
拼接后: [batch_size, total_length]              # [32, 400]
截断后: [batch_size, max_prompt_length]         # [32, 4096]

# 奖励张量
reward_tensor: [batch_size, response_length]    # [32, 50]
# 只有最后一个位置有非零值

# 推理阶段向量维度变化  
初始: [1, prompt_length]                        # [1, 150]
第一轮生成后: [1, prompt_length + response1]    # [1, 200]
加入搜索结果: [1, total_length]                 # [1, 400]
第二轮生成后: [1, final_length]                 # [1, 450]
```

## 4. 关键张量操作详解

### 4.1 张量拼接和填充 (TensorHelper)

**concatenate_with_padding方法**
```python
def concatenate_with_padding(self, tensors_list):
    """
    拼接多个张量并处理填充
    输入: [tensor1, tensor2, tensor3]
    tensor1: [batch_size, len1] - 原始prompt
    tensor2: [batch_size, len2] - 当前响应
    tensor3: [batch_size, len3] - 观察结果
    """
    pad_id = self.pad_token_id  # 通常是0或其他特殊token

    # 找到最大长度
    max_len = max(t.shape[1] for t in tensors_list)

    # 对每个张量进行左填充
    padded_tensors = []
    for tensor in tensors_list:
        if tensor.shape[1] < max_len:
            padding = torch.full((tensor.shape[0], max_len - tensor.shape[1]),
                               pad_id, dtype=tensor.dtype, device=tensor.device)
            padded_tensor = torch.cat([padding, tensor], dim=1)
        else:
            padded_tensor = tensor
        padded_tensors.append(padded_tensor)

    # 拼接所有张量
    concatenated = torch.cat(padded_tensors, dim=1)
    return concatenated

# 示例:
# tensor1: [[1, 2, 3, 4]]           # 原始prompt
# tensor2: [[5, 6]]                 # 响应 (较短)
# tensor3: [[7, 8, 9]]              # 观察 (中等长度)
#
# 填充后:
# tensor1: [[1, 2, 3, 4]]           # 无需填充
# tensor2: [[0, 0, 5, 6]]           # 左填充2个0
# tensor3: [[0, 7, 8, 9]]           # 左填充1个0
#
# 拼接结果: [[1, 2, 3, 4, 0, 0, 5, 6, 0, 7, 8, 9]]
```

### 4.2 信息掩码机制

**info_masked_concatenate_with_padding方法**
```python
def _info_masked_concatenate_with_padding(self, prompt, prompt_with_mask, response, info=None):
    """
    特殊的拼接方法，对信息部分进行掩码
    目的: 防止模型学习到搜索结果的token表示
    """
    tensors = [prompt, response]
    tensors_with_mask = [prompt_with_mask, response]

    if info is not None:
        tensors.append(info)
        # 创建信息掩码 - 用pad_token_id替换所有信息token
        info_mask = torch.full(info.size(), self.pad_token_id,
                              dtype=info.dtype, device=info.device)
        tensors_with_mask.append(info_mask)

    # 正常拼接
    concatenated = torch.cat(tensors, dim=1)
    # 掩码版本拼接
    concatenated_with_mask = torch.cat(tensors_with_mask, dim=1)

    return concatenated, concatenated_with_mask

# 示例:
# prompt: [[1, 2, 3]]               # "Answer the question"
# response: [[4, 5]]                # "<search>query</search>"
# info: [[6, 7, 8]]                 # "<information>results</information>"
#
# 正常版本: [[1, 2, 3, 4, 5, 6, 7, 8]]
# 掩码版本: [[1, 2, 3, 4, 5, 0, 0, 0]]  # 信息部分被掩码
```

### 4.3 注意力掩码和位置编码

**create_attention_mask方法**
```python
def create_attention_mask(self, input_ids):
    """
    创建注意力掩码
    1表示有效token，0表示padding token
    """
    return (input_ids != self.pad_token_id).long()

# 示例:
# input_ids: [[0, 0, 1, 2, 3, 0]]   # 0是pad_token_id
# attention_mask: [[0, 0, 1, 1, 1, 0]]

def create_position_ids(self, attention_mask):
    """
    基于attention_mask创建位置编码
    只对有效token分配递增的位置ID
    """
    position_ids = torch.zeros_like(attention_mask)
    position_ids[attention_mask == 1] = torch.arange(
        attention_mask.sum().item(), device=attention_mask.device
    )
    return position_ids

# 示例:
# attention_mask: [[0, 0, 1, 1, 1, 0]]
# position_ids: [[0, 0, 0, 1, 2, 0]]
```

## 5. 内存管理和优化

### 5.1 GPU内存优化

**多GPU填充处理**
```python
def _generate_with_gpu_padding(self, active_batch):
    """
    处理多GPU生成时的批次大小对齐
    确保batch_size能被GPU数量整除
    """
    num_gpus = self.config.num_gpus
    batch_size = active_batch.batch['input_ids'].shape[0]
    remainder = batch_size % num_gpus

    if remainder != 0:
        # 需要填充
        padding_size = num_gpus - remainder
        padded_batch = {}

        for k, v in active_batch.batch.items():
            # 使用第一个样本作为填充模板
            pad_sequence = v[0:1].repeat(padding_size, *[1] * (len(v.shape) - 1))
            padded_batch[k] = torch.cat([v, pad_sequence], dim=0)

        # 生成后移除填充
        output = self.actor_rollout_wg.generate_sequences(padded_active_batch)
        trimmed_batch = {k: v[:-padding_size] for k, v in output.batch.items()}
        return trimmed_batch

    return self.actor_rollout_wg.generate_sequences(active_batch)

# 示例:
# 原始batch_size=5, num_gpus=4
# remainder = 5 % 4 = 1
# padding_size = 4 - 1 = 3
# 填充后batch_size=8, 生成完成后截断回5
```

### 5.2 动态长度处理

**cut_to_effective_len方法**
```python
def cut_to_effective_len(self, batch_dict, keys):
    """
    根据有效长度截断张量，节省内存和计算
    """
    if 'attention_mask' in batch_dict:
        effective_len = batch_dict['attention_mask'].sum(dim=1).max().item()
    else:
        effective_len = batch_dict['input_ids'].shape[1]

    for key in keys:
        if key in batch_dict:
            batch_dict[key] = batch_dict[key][:, -effective_len:]

    return batch_dict

# 示例:
# input_ids: [[0, 0, 1, 2, 3, 4, 5, 0, 0]]     # 长度9
# attention_mask: [[0, 0, 1, 1, 1, 1, 1, 0, 0]] # 有效长度5
# 截断后: [[1, 2, 3, 4, 5]]                     # 只保留有效部分
```

## 6. 完整示例：单个样本的完整流程

让我们跟踪一个具体样本从开始到结束的完整数据变化：

**初始状态**
```python
# 原始问题
question = "What is the capital of France?"

# 预处理后的prompt
prompt_text = "Answer the given question. You must conduct reasoning inside <think> and </think>... Question: What is the capital of France?\n"

# Tokenization (假设的token IDs)
initial_ids = [1, 22550, 279, 2728, 3488, 13, 1472, 2011, 6929, 33811, 4871, 1148, 374, 279, 6864, 315, 9822, 30, 198]
# 形状: [1, 18]
```

**第一轮生成**
```python
# 模型生成
response1_text = "<think>I know this is a basic geography question.</think><answer>Paris</answer>"
response1_ids = [27, 27, think, 29, 40, 1440, 420, 374, 264, 6913, 54242, 3488, 13, 524, think, 29, 27, answer, 29, 60704, 524, answer, 29]
# 形状: [1, 23]

# 拼接后的完整序列
full_sequence = initial_ids + response1_ids
# 形状: [1, 41]

# 奖励计算
extracted_answer = "Paris"
ground_truth = ["Paris"]
reward = 1.0  # 精确匹配

# 奖励张量 (只在最后一个response token位置给出奖励)
reward_tensor = torch.zeros(1, 23)  # response长度
reward_tensor[0, 22] = 1.0  # 最后一个位置
```

**训练更新**
```python
# PPO训练中使用的数据
training_data = {
    'input_ids': full_sequence,           # [1, 41] - 完整序列
    'attention_mask': torch.ones(1, 41),  # [1, 41] - 全部有效
    'rewards': reward_tensor,             # [1, 23] - 奖励信号
    'old_log_probs': old_log_probs,       # [1, 23] - 旧策略概率
    'values': values                      # [1, 23] - 价值估计
}
```

## 7. 复杂多轮搜索示例

让我们看一个需要多轮搜索的复杂问题：

**问题**: "Mike Barnett negotiated many contracts including which player that went on to become general manager of CSKA Moscow?"

### 7.1 多轮交互的完整数据流

**初始状态**
```python
# 初始prompt tokenization
initial_text = "Answer the given question... Question: Mike Barnett negotiated many contracts including which player that went on to become general manager of CSKA Moscow?\n"
initial_ids = tokenizer.encode(initial_text)  # [1, 150] 假设150个token
```

**第一轮：探索性搜索**
```python
# 第一轮生成
turn1_response = "<think>I need to find information about Mike Barnett and his contract negotiations.</think><search>Mike Barnett sports agent contracts</search>"
turn1_ids = tokenizer.encode(turn1_response, add_special_tokens=False)  # [45] token

# 搜索结果
search1_result = "\n\n<information>Doc 1(Title: Mike Barnett) Mike Barnett is a prominent sports agent who has negotiated contracts for numerous NHL players including Pavel Bure, Sergei Fedorov, and Alexander Mogilny...</information>\n\n"
search1_ids = tokenizer.encode(search1_result, add_special_tokens=False)  # [85] token

# 第一轮后的完整状态
state_after_turn1 = torch.cat([
    initial_ids,     # [150]
    turn1_ids,       # [45]
    search1_ids      # [85]
], dim=1)           # [280] total
```

**第二轮：精确搜索**
```python
# 基于第一轮结果的第二轮生成
turn2_response = "<think>I found that Mike Barnett represented Sergei Fedorov. Let me search specifically about Sergei Fedorov and CSKA Moscow.</think><search>Sergei Fedorov CSKA Moscow general manager</search>"
turn2_ids = tokenizer.encode(turn2_response, add_special_tokens=False)  # [55] token

# 第二次搜索结果
search2_result = "\n\n<information>Doc 1(Title: Sergei Fedorov) Sergei Fedorov became the general manager of CSKA Moscow in 2018...</information>\n\n"
search2_ids = tokenizer.encode(search2_result, add_special_tokens=False)  # [65] token

# 第二轮后的完整状态
state_after_turn2 = torch.cat([
    state_after_turn1,  # [280]
    turn2_ids,          # [55]
    search2_ids         # [65]
], dim=1)              # [400] total
```

**最终回答**
```python
# 最终生成
final_response = "<think>Now I have clear information that Sergei Fedorov, who was represented by Mike Barnett, became the general manager of CSKA Moscow.</think><answer>Sergei Fedorov</answer>"
final_ids = tokenizer.encode(final_response, add_special_tokens=False)  # [40] token

# 完整的最终状态
final_state = torch.cat([
    state_after_turn2,  # [400]
    final_ids           # [40]
], dim=1)              # [440] total
```

### 7.2 多轮状态管理

**活跃掩码管理**
```python
# 在generation.py的run_llm_loop中
active_mask = torch.ones(batch_size, dtype=torch.bool)  # [32] - 所有样本都活跃
turns_stats = torch.ones(batch_size, dtype=torch.int)   # [32] - 轮数统计
valid_action_stats = torch.zeros(batch_size, dtype=torch.int)  # [32] - 有效动作统计

for step in range(max_turns):  # 最多2轮
    # 生成响应
    gen_output = self.generate_sequences(rollings)

    # 执行动作
    next_obs, dones, valid_action, is_search = self.execute_predictions(responses_str)

    # 更新活跃状态
    curr_active_mask = torch.tensor([not done for done in dones], dtype=torch.bool)
    active_mask = active_mask * curr_active_mask

    # 统计更新
    turns_stats[curr_active_mask] += 1
    valid_action_stats += torch.tensor(valid_action, dtype=torch.int)

    # 如果没有活跃样本，退出循环
    if not active_mask.sum():
        break

# 最终统计示例:
# active_mask: [True, False, True, ...]  # 哪些样本还在继续
# turns_stats: [2, 1, 2, ...]           # 每个样本用了几轮
# valid_action_stats: [2, 1, 2, ...]    # 每个样本有几个有效动作
```

## 8. PPO训练中的数据流动

### 8.1 经验收集阶段

**Rollout数据收集**
```python
# 在RayPPOTrainer中
def collect_rollouts(self):
    # 生成轨迹
    rollout_data = self.generation_manager.run_llm_loop(batch_data)

    # rollout_data包含:
    rollout_data = {
        'input_ids': tensor,        # [batch_size, seq_len] - 完整序列
        'responses': tensor,        # [batch_size, response_len] - 响应部分
        'attention_mask': tensor,   # [batch_size, seq_len] - 注意力掩码
        'info_mask': tensor,        # [batch_size, seq_len] - 信息掩码
        'prompts': tensor,          # [batch_size, prompt_len] - 原始提示
        'meta_info': {
            'turns_stats': [2, 1, 2, ...],
            'valid_action_stats': [2, 1, 2, ...],
            'valid_search_stats': [1, 0, 1, ...]
        }
    }

    return rollout_data
```

**价值估计和优势计算**
```python
# Critic网络计算价值
values = self.critic_worker.compute_values(rollout_data)
# values: [batch_size, response_len] - 每个位置的价值估计

# 奖励计算
rewards = self.reward_manager(rollout_data)
# rewards: [batch_size, response_len] - 只有最后位置非零

# GAE优势计算
advantages = self.compute_gae_advantages(rewards, values)
# advantages: [batch_size, response_len] - 优势估计
```

### 8.2 策略更新阶段

**PPO损失计算**
```python
def compute_ppo_loss(self, batch_data):
    # 前向传播获取新的log概率
    new_log_probs = self.actor.forward(batch_data['input_ids'])
    # new_log_probs: [batch_size, response_len, vocab_size]

    # 计算概率比率
    ratio = torch.exp(new_log_probs - batch_data['old_log_probs'])
    # ratio: [batch_size, response_len]

    # PPO clipped损失
    advantages = batch_data['advantages']
    clip_ratio = 0.2

    surr1 = ratio * advantages
    surr2 = torch.clamp(ratio, 1 - clip_ratio, 1 + clip_ratio) * advantages
    policy_loss = -torch.min(surr1, surr2).mean()

    # 价值损失
    values = self.critic.forward(batch_data['input_ids'])
    value_loss = F.mse_loss(values, batch_data['returns'])

    # 总损失
    total_loss = policy_loss + 0.5 * value_loss

    return total_loss
```

### 8.3 批次处理和内存优化

**微批次处理**
```python
def process_mini_batches(self, rollout_data):
    batch_size = rollout_data['input_ids'].shape[0]
    mini_batch_size = self.config.ppo_mini_batch_size  # 例如256

    # 随机打乱
    indices = torch.randperm(batch_size)

    for start_idx in range(0, batch_size, mini_batch_size):
        end_idx = min(start_idx + mini_batch_size, batch_size)
        mini_batch_indices = indices[start_idx:end_idx]

        # 提取微批次
        mini_batch = {
            k: v[mini_batch_indices] if isinstance(v, torch.Tensor) else v
            for k, v in rollout_data.items()
        }

        # 计算损失和更新
        loss = self.compute_ppo_loss(mini_batch)
        loss.backward()

        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(self.actor.parameters(), max_norm=1.0)

        # 优化器步骤
        self.actor_optimizer.step()
        self.actor_optimizer.zero_grad()
```

## 9. 关键性能指标和监控

### 9.1 训练监控指标

```python
# 在训练过程中收集的关键指标
training_metrics = {
    'reward/mean': rewards.mean().item(),           # 平均奖励
    'reward/std': rewards.std().item(),             # 奖励标准差
    'policy/entropy': entropy.mean().item(),        # 策略熵
    'policy/kl_divergence': kl_div.mean().item(),   # KL散度
    'value/mean': values.mean().item(),             # 平均价值估计
    'advantage/mean': advantages.mean().item(),     # 平均优势
    'turns/mean': np.mean(turns_stats),             # 平均轮数
    'valid_actions/ratio': np.mean(valid_action_stats) / np.mean(turns_stats),  # 有效动作比例
    'search/ratio': np.mean(valid_search_stats) / np.mean(turns_stats)          # 搜索使用比例
}
```

### 9.2 内存使用监控

```python
# GPU内存监控
def monitor_memory_usage():
    if torch.cuda.is_available():
        memory_allocated = torch.cuda.memory_allocated() / 1024**3  # GB
        memory_reserved = torch.cuda.memory_reserved() / 1024**3    # GB

        print(f"GPU Memory - Allocated: {memory_allocated:.2f}GB, Reserved: {memory_reserved:.2f}GB")

        # 在关键点清理内存
        torch.cuda.empty_cache()
```

这个完整的数据流动过程展示了Search-R1如何通过精确的张量操作、内存管理和多轮交互机制实现高效的推理-搜索结合训练，每个步骤都经过精心设计以确保训练的稳定性和推理的准确性。
