# Search-R1 数据流动总结

## 核心数据流动路径

### 1. 数据预处理 → 训练数据
```
原始QA对 → 提示模板构建 → Tokenization → Parquet格式
{question, answers} → {prompt, reward_model} → token_ids → 训练文件
```

### 2. 训练时的数据流动
```
批次采样 → 多轮生成循环 → 奖励计算 → PPO更新
[32, seq_len] → [32, response_len] → [32, 1] → 梯度更新
```

### 3. 推理时的数据流动
```
用户问题 → 多轮交互循环 → 最终答案
[1, prompt_len] → [1, growing_len] → 文本输出
```

## 关键张量变化模式

### 训练阶段张量演化
1. **初始状态**: `[batch_size, prompt_length]`
2. **生成响应**: `[batch_size, response_length]`
3. **搜索结果**: `[batch_size, observation_length]`
4. **状态拼接**: `[batch_size, cumulative_length]`
5. **奖励计算**: `[batch_size, response_length]` (稀疏，只有最后位置非零)

### 内存优化策略
- **动态截断**: 根据有效长度截断序列
- **批次填充**: 多GPU训练时的批次大小对齐
- **信息掩码**: 防止模型学习搜索结果token
- **梯度检查点**: 减少前向传播内存占用

## 多轮交互的状态管理

### 状态跟踪变量
```python
active_mask: [batch_size]        # 哪些样本还在继续交互
turns_stats: [batch_size]        # 每个样本使用的轮数
valid_action_stats: [batch_size] # 每个样本的有效动作数
valid_search_stats: [batch_size] # 每个样本的搜索次数
```

### 交互循环逻辑
```python
for turn in range(max_turns):
    if not active_mask.sum(): break
    
    # 1. 生成响应
    responses = model.generate(current_state)
    
    # 2. 解析动作
    actions, contents = parse_actions(responses)
    
    # 3. 执行动作
    if action == "search":
        search_results = search_engine(content)
        current_state = update_state(current_state, responses, search_results)
        continue
    elif action == "answer":
        break
    else:
        # 无效动作，给出提示继续
        current_state = update_state(current_state, responses, error_message)
```

## 奖励机制设计

### 奖励计算原理
- **稀疏奖励**: 只在序列最后一个token位置给出奖励
- **精确匹配**: 基于EM (Exact Match) 算法
- **格式奖励**: 鼓励正确的输出格式 (`<answer>...</answer>`)

### 奖励张量构建
```python
reward_tensor = torch.zeros_like(responses, dtype=torch.float32)
# 形状: [batch_size, response_length]

# 只在最后一个有效位置设置奖励
for i, (response, ground_truth) in enumerate(zip(responses, ground_truths)):
    valid_length = attention_mask[i].sum().item()
    score = compute_em_score(response, ground_truth)
    reward_tensor[i, valid_length - 1] = score
```

## 检索系统集成

### 检索API调用流程
```python
# 1. 批量查询准备
queries = extract_search_queries(responses)

# 2. API调用
payload = {"queries": queries, "topk": 3, "return_scores": True}
results = requests.post("http://127.0.0.1:8000/retrieve", json=payload)

# 3. 结果格式化
formatted_results = []
for result in results['result']:
    formatted = format_passages_to_string(result)
    formatted_results.append(f"<information>{formatted}</information>")
```

### 搜索结果处理
- **格式标准化**: 统一的 `<information>...</information>` 格式
- **长度控制**: 限制搜索结果长度避免超出模型上下文
- **批量处理**: 支持批量搜索提高效率

## 性能优化要点

### 内存优化
1. **FSDP配置**: 参数分片和卸载
2. **梯度检查点**: 减少激活值存储
3. **动态批处理**: 根据序列长度调整批次大小
4. **及时清理**: 在关键点清理GPU缓存

### 计算优化
1. **张量操作**: 使用高效的PyTorch操作
2. **并行处理**: 多GPU生成和搜索
3. **缓存机制**: 重用计算结果
4. **异步处理**: 搜索和生成的异步执行

## 调试和监控

### 关键监控指标
- **奖励趋势**: 训练过程中的奖励变化
- **动作有效性**: 有效动作的比例
- **搜索使用率**: 模型使用搜索的频率
- **轮数分布**: 不同样本使用的交互轮数

### 常见问题诊断
1. **奖励不收敛**: 检查奖励函数和数据质量
2. **内存溢出**: 调整批次大小和序列长度
3. **搜索失效**: 验证检索服务和API连接
4. **格式错误**: 检查模型输出的标签格式

## 扩展和定制指南

### 新数据集适配
1. 修改数据预处理脚本
2. 实现对应的奖励计算函数
3. 调整提示模板和格式要求

### 新搜索引擎集成
1. 继承BaseRetriever基类
2. 实现搜索接口方法
3. 配置相应的服务启动脚本

### 模型架构调整
1. 修改生成配置参数
2. 调整网络结构和训练超参数
3. 优化张量操作和内存使用

这个数据流动总结展示了Search-R1从原始数据到最终输出的完整路径，每个环节都有明确的数据格式和处理逻辑，为理解和扩展系统提供了清晰的指导。
