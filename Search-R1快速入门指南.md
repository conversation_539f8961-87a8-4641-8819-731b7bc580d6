# Search-R1 快速入门指南

## 项目简介

Search-R1 是一个训练能够推理和搜索的大语言模型的强化学习框架。模型学会在回答问题时：
1. 先进行思考 `<think>...</think>`
2. 必要时搜索信息 `<search>...</search>`
3. 获取搜索结果 `<information>...</information>`
4. 最终给出答案 `<answer>...</answer>`

## 核心文件速览（按重要性排序）

### 🔥 必读核心文件

1. **search_r1/llm_agent/generation.py** - 最重要的文件
   - `LLMGenerationManager`类：管理整个推理-搜索循环
   - `run_llm_loop()`：主要的生成循环逻辑
   - `execute_predictions()`：执行搜索或回答动作

2. **search_r1/search/retrieval_server.py** - 搜索引擎实现
   - `DenseRetriever`：密集检索器（如E5）
   - `BM25Retriever`：稀疏检索器
   - FastAPI服务接口

3. **train_ppo.sh** - 训练配置脚本
   - 包含所有训练超参数
   - 模型路径、数据路径配置

### 📚 重要支撑文件

4. **verl/trainer/main_ppo.py** - PPO训练主程序
   - `RewardManager`：奖励计算逻辑

5. **scripts/data_process/nq_search.py** - 数据预处理
   - 将QA数据转换为训练格式

6. **infer.py** - 推理示例
   - 展示如何使用训练好的模型

## 快速理解核心流程

### 训练流程
```
原始QA数据 → 数据预处理 → 启动检索服务 → PPO训练 → 保存模型
```

### 推理流程
```
用户问题 → 模型思考 → 决定搜索 → 获取信息 → 继续推理 → 最终答案
```

### 关键交互循环
```python
while not done:
    # 1. 模型生成响应
    response = model.generate(prompt)
    
    # 2. 解析动作
    if "<search>" in response:
        # 执行搜索
        search_results = search_engine(query)
        prompt += f"<information>{search_results}</information>"
    elif "<answer>" in response:
        # 给出最终答案
        done = True
    else:
        # 无效动作，给出提示
        prompt += "Invalid action, try again"
```

## 代码阅读建议路径

### 🚀 快速理解路径（2-3小时）
1. 阅读README.md了解项目背景
2. 查看train_ppo.sh了解训练配置
3. 运行infer.py体验推理过程
4. 阅读generation.py的`run_llm_loop()`方法

### 🔍 深入理解路径（1-2天）
1. 完整阅读generation.py，理解所有方法
2. 阅读retrieval_server.py，理解检索实现
3. 阅读main_ppo.py，理解训练逻辑
4. 查看数据处理脚本nq_search.py

### 🛠️ 开发定制路径（3-5天）
1. 研究verl框架的整体架构
2. 理解奖励函数设计（qa_em.py）
3. 学习多节点训练配置
4. 探索不同搜索引擎集成方法

## 关键概念解释

### 1. 多轮交互
- 模型可以进行多轮搜索，每次搜索后都会更新上下文
- 通过`max_turns`参数控制最大交互轮数

### 2. 奖励设计
- 只在生成序列的最后一个token给出奖励
- 基于精确匹配（EM）计算QA任务的奖励
- 支持格式奖励，鼓励正确的输出格式

### 3. 张量处理
- 使用attention mask控制模型看到的内容
- 信息掩码防止模型学习搜索结果的token
- 动态拼接和截断处理变长序列

### 4. 检索集成
- 支持本地BM25、密集检索、在线搜索API
- 通过HTTP API调用检索服务
- 批量处理提高效率

## 实验和调试技巧

### 1. 小规模验证
```bash
# 使用小数据集快速验证流程
python scripts/data_process/nq_search.py  # 处理少量数据
bash train_ppo.sh  # 设置小的batch_size和steps
```

### 2. 监控关键指标
- 奖励分数趋势
- 有效动作比例（valid_action_stats）
- 搜索使用频率（valid_search_stats）
- 活跃轨迹数量（ACTIVE_TRAJ_NUM）

### 3. 常见问题排查
- 检索服务是否正常：`curl http://127.0.0.1:8000/retrieve`
- 数据格式是否正确：检查parquet文件内容
- 内存是否足够：调整batch_size和offload参数

## 扩展方向

### 1. 新数据集适配
- 修改数据处理脚本
- 添加对应的奖励计算函数
- 调整提示模板

### 2. 新搜索引擎集成
- 继承BaseRetriever类
- 实现搜索接口
- 启动对应的服务

### 3. 模型优化
- 调整网络架构
- 改进奖励函数设计
- 优化训练超参数

## 下一步行动建议

1. **初学者**：先运行完整的训练和推理流程，理解整体工作原理
2. **研究者**：重点关注奖励设计和训练算法，探索改进方向
3. **工程师**：关注系统架构和性能优化，考虑生产部署
4. **应用开发者**：学习如何适配新的数据集和搜索引擎

这个快速入门指南帮助你在最短时间内理解Search-R1的核心概念和实现，建议结合详细的代码阅读指导文档深入学习。
